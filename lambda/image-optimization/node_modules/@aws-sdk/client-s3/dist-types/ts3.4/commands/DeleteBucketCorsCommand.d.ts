import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketCorsRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketCorsCommandInput extends DeleteBucketCorsRequest {}
export interface DeleteBucketCorsCommandOutput extends __MetadataBearer {}
declare const DeleteBucketCorsCommand_base: {
  new (
    input: DeleteBucketCorsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketCorsCommandInput,
    DeleteBucketCorsCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketCorsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketCorsCommandInput,
    DeleteBucketCorsCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketCorsCommand extends DeleteBucketCorsCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketCorsRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketCorsCommandInput;
      output: DeleteBucketCorsCommandOutput;
    };
  };
}
