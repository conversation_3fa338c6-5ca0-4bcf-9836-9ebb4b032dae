import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketMetricsConfigurationRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketMetricsConfigurationCommandInput
  extends DeleteBucketMetricsConfigurationRequest {}
export interface DeleteBucketMetricsConfigurationCommandOutput
  extends __MetadataBearer {}
declare const DeleteBucketMetricsConfigurationCommand_base: {
  new (
    input: DeleteBucketMetricsConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketMetricsConfigurationCommandInput,
    DeleteBucketMetricsConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketMetricsConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketMetricsConfigurationCommandInput,
    DeleteBucketMetricsConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketMetricsConfigurationCommand extends DeleteBucketMetricsConfigurationCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketMetricsConfigurationRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketMetricsConfigurationCommandInput;
      output: DeleteBucketMetricsConfigurationCommandOutput;
    };
  };
}
