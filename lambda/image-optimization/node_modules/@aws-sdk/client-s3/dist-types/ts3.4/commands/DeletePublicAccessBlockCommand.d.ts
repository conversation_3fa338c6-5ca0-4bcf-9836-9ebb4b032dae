import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeletePublicAccessBlockRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeletePublicAccessBlockCommandInput
  extends DeletePublicAccessBlockRequest {}
export interface DeletePublicAccessBlockCommandOutput
  extends __MetadataBearer {}
declare const DeletePublicAccessBlockCommand_base: {
  new (
    input: DeletePublicAccessBlockCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeletePublicAccessBlockCommandInput,
    DeletePublicAccessBlockCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeletePublicAccessBlockCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeletePublicAccessBlockCommandInput,
    DeletePublicAccessBlockCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeletePublicAccessBlockCommand extends DeletePublicAccessBlockCommand_base {
  protected static __types: {
    api: {
      input: DeletePublicAccessBlockRequest;
      output: {};
    };
    sdk: {
      input: DeletePublicAccessBlockCommandInput;
      output: DeletePublicAccessBlockCommandOutput;
    };
  };
}
