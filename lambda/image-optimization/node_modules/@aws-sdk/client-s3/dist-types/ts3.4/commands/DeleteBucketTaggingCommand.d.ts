import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketTaggingRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketTaggingCommandInput
  extends DeleteBucketTaggingRequest {}
export interface DeleteBucketTaggingCommandOutput extends __MetadataBearer {}
declare const DeleteBucketTaggingCommand_base: {
  new (
    input: DeleteBucketTaggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketTaggingCommandInput,
    DeleteBucketTaggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketTaggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketTaggingCommandInput,
    DeleteBucketTaggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketTaggingCommand extends DeleteBucketTaggingCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketTaggingRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketTaggingCommandInput;
      output: DeleteBucketTaggingCommandOutput;
    };
  };
}
