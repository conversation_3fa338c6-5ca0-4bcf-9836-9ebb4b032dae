import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketLoggingRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketLoggingCommandInput extends PutBucketLoggingRequest {}
export interface PutBucketLoggingCommandOutput extends __MetadataBearer {}
declare const PutBucketLoggingCommand_base: {
  new (
    input: PutBucketLoggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketLoggingCommandInput,
    PutBucketLoggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutBucketLoggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketLoggingCommandInput,
    PutBucketLoggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketLoggingCommand extends PutBucketLoggingCommand_base {
  protected static __types: {
    api: {
      input: PutBucketLoggingRequest;
      output: {};
    };
    sdk: {
      input: PutBucketLoggingCommandInput;
      output: PutBucketLoggingCommandOutput;
    };
  };
}
