import { Paginator } from "@smithy/types";
import {
  ListDirectoryBucketsCommandInput,
  ListDirectoryBucketsCommandOutput,
} from "../commands/ListDirectoryBucketsCommand";
import { S3PaginationConfiguration } from "./Interfaces";
export declare const paginateListDirectoryBuckets: (
  config: S3PaginationConfiguration,
  input: ListDirectoryBucketsCommandInput,
  ...rest: any[]
) => Paginator<ListDirectoryBucketsCommandOutput>;
